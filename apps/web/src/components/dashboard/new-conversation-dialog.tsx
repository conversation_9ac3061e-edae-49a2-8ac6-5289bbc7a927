'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { X, Search, Plus } from 'lucide-react';
import { trpc } from '../../lib/trpc';
import { LoadingSpinner } from '../ui/loading-spinner';

interface NewConversationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConversationCreated?: (conversationId: string) => void;
}

export function NewConversationDialog({ 
  isOpen, 
  onClose, 
  onConversationCreated 
}: NewConversationDialogProps) {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>([]);
  const [participantSearch, setParticipantSearch] = useState('');
  const [conversationType, setConversationType] = useState<'DIRECT' | 'GROUP' | 'SUPPORT'>('DIRECT');
  const [contextType, setContextType] = useState<'quote' | 'order' | 'inquiry' | 'product' | undefined>();
  const [contextId, setContextId] = useState<string>('');

  // Fetch users for participant selection
  const { data: usersData, isLoading: usersLoading } = trpc.users.getAll.useQuery({
    page: 1,
    limit: 50,
    search: participantSearch,
  });

  // Create conversation mutation
  const createConversationMutation = trpc.messages.createConversation.useMutation({
    onSuccess: async (data) => {
      if (!data) return;

      // Send initial message if provided
      if (message.trim()) {
        try {
          await sendMessageMutation.mutateAsync({
            conversationId: data.id,
            content: message.trim(),
            messageType: 'TEXT',
          });
          handleSuccess(data.id);
        } catch (error) {
          console.error('Failed to send initial message:', error);
          handleSuccess(data.id); // Still navigate even if message fails
        }
      } else {
        handleSuccess(data.id);
      }
    },
  });

  // Send initial message mutation
  const sendMessageMutation = trpc.messages.sendMessage.useMutation();

  const handleSuccess = (conversationId: string) => {
    // Reset form
    setSubject('');
    setMessage('');
    setSelectedParticipants([]);
    setParticipantSearch('');
    setContextType(undefined);
    setContextId('');
    
    // Invalidate queries
    trpc.useContext().messages.getConversations.invalidate();
    trpc.useContext().messages.getUnreadCount.invalidate();
    
    // Notify parent and close
    onConversationCreated?.(conversationId);
    onClose();
  };

  const handleCreateConversation = () => {
    if (selectedParticipants.length === 0) return;

    createConversationMutation.mutate({
      type: conversationType,
      subject: subject.trim() || undefined,
      participantIds: selectedParticipants,
      contextType,
      contextId: contextId || undefined,
    });
  };

  const addParticipant = (userId: string) => {
    if (!selectedParticipants.includes(userId)) {
      setSelectedParticipants([...selectedParticipants, userId]);
    }
    setParticipantSearch('');
  };

  const removeParticipant = (userId: string) => {
    setSelectedParticipants(selectedParticipants.filter(id => id !== userId));
  };

  const users = usersData?.data || [];
  const filteredUsers = users.filter(user => 
    !selectedParticipants.includes(user.id) &&
    (user.firstName.toLowerCase().includes(participantSearch.toLowerCase()) ||
     user.lastName.toLowerCase().includes(participantSearch.toLowerCase()) ||
     user.email.toLowerCase().includes(participantSearch.toLowerCase()))
  );

  const selectedUsers = users.filter(user => selectedParticipants.includes(user.id));

  const isLoading = createConversationMutation.isLoading || sendMessageMutation.isLoading;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>New Conversation</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Conversation Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Conversation Type</Label>
            <Select value={conversationType} onValueChange={(value: any) => setConversationType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="DIRECT">Direct Message</SelectItem>
                <SelectItem value="GROUP">Group Chat</SelectItem>
                <SelectItem value="SUPPORT">Support Ticket</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">Subject (Optional)</Label>
            <Input
              id="subject"
              placeholder="Enter conversation subject..."
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
            />
          </div>

          {/* Context (Optional) */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contextType">Related To (Optional)</Label>
              <Select value={contextType || ''} onValueChange={(value: any) => setContextType(value || undefined)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select context..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">None</SelectItem>
                  <SelectItem value="quote">Quote</SelectItem>
                  <SelectItem value="order">Order</SelectItem>
                  <SelectItem value="inquiry">Inquiry</SelectItem>
                  <SelectItem value="product">Product</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {contextType && (
              <div className="space-y-2">
                <Label htmlFor="contextId">ID</Label>
                <Input
                  id="contextId"
                  placeholder={`Enter ${contextType} ID...`}
                  value={contextId}
                  onChange={(e) => setContextId(e.target.value)}
                />
              </div>
            )}
          </div>

          {/* Participants */}
          <div className="space-y-2">
            <Label>Participants</Label>
            
            {/* Selected Participants */}
            {selectedUsers.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-2">
                {selectedUsers.map(user => (
                  <Badge key={user.id} variant="secondary" className="flex items-center gap-1">
                    {user.firstName} {user.lastName}
                    <button
                      onClick={() => removeParticipant(user.id)}
                      className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Participant Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search users to add..."
                value={participantSearch}
                onChange={(e) => setParticipantSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Search Results */}
            {participantSearch && (
              <div className="border rounded-md max-h-40 overflow-y-auto">
                {usersLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <LoadingSpinner />
                  </div>
                ) : filteredUsers.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">
                    No users found
                  </div>
                ) : (
                  <div className="p-2">
                    {filteredUsers.map(user => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer"
                        onClick={() => addParticipant(user.id)}
                      >
                        <div>
                          <p className="text-sm font-medium">
                            {user.firstName} {user.lastName}
                          </p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                        </div>
                        <Plus className="w-4 h-4 text-gray-400" />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Initial Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Initial Message (Optional)</Label>
            <Textarea
              id="message"
              placeholder="Type your message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateConversation}
              disabled={selectedParticipants.length === 0 || isLoading}
            >
              {isLoading ? (
                <>
                  <LoadingSpinner className="w-4 h-4 mr-2" />
                  Creating...
                </>
              ) : (
                'Create Conversation'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
