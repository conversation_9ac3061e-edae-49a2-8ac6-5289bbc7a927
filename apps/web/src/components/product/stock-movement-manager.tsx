'use client';

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Package, 
  Plus, 
  ArrowUp, 
  ArrowDown, 
  RefreshCw, 
  Truck, 
  RotateCcw, 
  AlertTriangle,
  Loader2,
  Calendar
} from 'lucide-react';
import { trpc } from '@/lib/trpc';

interface StockMovementManagerProps {
  productId: string;
  factoryId: string;
  inventoryLocations: any[];
}

interface StockMovement {
  id: string;
  movementType: 'INBOUND' | 'OUTBOUND' | 'ADJUSTMENT' | 'TRANSFER' | 'RETURN' | 'DAMAGED' | 'EXPIRED';
  quantity: number;
  reason: string | null;
  notes: string | null;
  batchNumber: string | null;
  expiryDate: string | null;
  unitCost: string | null;
  totalCost: string | null;
  currency: string | null;
  referenceId: string | null;
  referenceType: string | null;
  createdAt: string;
  createdBy: string | null;
}

const movementTypes = [
  { value: 'INBOUND', label: 'Inbound', icon: ArrowUp, color: 'text-green-600' },
  { value: 'OUTBOUND', label: 'Outbound', icon: ArrowDown, color: 'text-red-600' },
  { value: 'ADJUSTMENT', label: 'Adjustment', icon: RefreshCw, color: 'text-blue-600' },
  { value: 'TRANSFER', label: 'Transfer', icon: Truck, color: 'text-purple-600' },
  { value: 'RETURN', label: 'Return', icon: RotateCcw, color: 'text-orange-600' },
  { value: 'DAMAGED', label: 'Damaged', icon: AlertTriangle, color: 'text-red-500' },
  { value: 'EXPIRED', label: 'Expired', icon: Calendar, color: 'text-gray-500' }
];

export function StockMovementManager({ productId, factoryId, inventoryLocations }: StockMovementManagerProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState('');
  const [movementData, setMovementData] = useState({
    movementType: 'INBOUND' as const,
    quantity: 0,
    reason: '',
    notes: '',
    batchNumber: '',
    expiryDate: '',
    unitCost: 0,
    totalCost: 0,
    currency: 'USD' as const,
    referenceId: '',
    referenceType: ''
  });

  // tRPC queries
  const { 
    data: movements, 
    refetch: refetchMovements, 
    isLoading: movementsLoading 
  } = trpc.products.getStockMovements?.useQuery({
    productId,
    factoryId
  }) || { data: [], refetch: () => {}, isLoading: false };

  // tRPC mutations
  const recordMovementMutation = trpc.products.recordStockMovement.useMutation({
    onSuccess: () => {
      refetchMovements();
      setShowAddForm(false);
      setSelectedLocation('');
      setMovementData({
        movementType: 'INBOUND',
        quantity: 0,
        reason: '',
        notes: '',
        batchNumber: '',
        expiryDate: '',
        unitCost: 0,
        totalCost: 0,
        currency: 'USD',
        referenceId: '',
        referenceType: ''
      });
    }
  });

  const handleInputChange = (field: string, value: any) => {
    setMovementData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedLocation) return;

    try {
      await recordMovementMutation.mutateAsync({
        inventoryLocationId: selectedLocation,
        movementData: {
          ...movementData,
          expiryDate: movementData.expiryDate ? new Date(movementData.expiryDate).toISOString() : undefined
        }
      });
    } catch (error) {
      console.error('Failed to record stock movement:', error);
    }
  };

  const getMovementTypeInfo = (type: string) => {
    return movementTypes.find(mt => mt.value === type) || movementTypes[0];
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Package className="w-5 h-5 mr-2" />
              Stock Movement History
            </div>
            <Button
              onClick={() => setShowAddForm(true)}
              size="sm"
              className="flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Record Movement
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Add Movement Form */}
          {showAddForm && (
            <Card className="border-2 border-dashed border-blue-300 mb-6">
              <CardHeader>
                <CardTitle className="flex items-center justify-between text-lg">
                  Record Stock Movement
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAddForm(false)}
                  >
                    ×
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="location">Location *</Label>
                      <select
                        id="location"
                        value={selectedLocation}
                        onChange={(e) => setSelectedLocation(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        <option value="">Select Location</option>
                        {inventoryLocations.map((location) => (
                          <option key={location.id} value={location.id}>
                            {location.locationName}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="movementType">Movement Type *</Label>
                      <select
                        id="movementType"
                        value={movementData.movementType}
                        onChange={(e) => handleInputChange('movementType', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      >
                        {movementTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="quantity">Quantity *</Label>
                      <Input
                        id="quantity"
                        type="number"
                        value={movementData.quantity}
                        onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 0)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="reason">Reason</Label>
                      <Input
                        id="reason"
                        value={movementData.reason}
                        onChange={(e) => handleInputChange('reason', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="batchNumber">Batch Number</Label>
                      <Input
                        id="batchNumber"
                        value={movementData.batchNumber}
                        onChange={(e) => handleInputChange('batchNumber', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="expiryDate">Expiry Date</Label>
                      <Input
                        id="expiryDate"
                        type="date"
                        value={movementData.expiryDate}
                        onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="unitCost">Unit Cost</Label>
                      <Input
                        id="unitCost"
                        type="number"
                        step="0.01"
                        value={movementData.unitCost}
                        onChange={(e) => handleInputChange('unitCost', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="totalCost">Total Cost</Label>
                      <Input
                        id="totalCost"
                        type="number"
                        step="0.01"
                        value={movementData.totalCost}
                        onChange={(e) => handleInputChange('totalCost', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      value={movementData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={recordMovementMutation.isLoading}>
                      {recordMovementMutation.isLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Recording...
                        </>
                      ) : (
                        'Record Movement'
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Movement History */}
          {movementsLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>Loading movement history...</span>
            </div>
          ) : movements && movements.length > 0 ? (
            <div className="space-y-3">
              {movements.map((movement: StockMovement) => {
                const typeInfo = getMovementTypeInfo(movement.movementType);
                const Icon = typeInfo.icon;
                
                return (
                  <Card key={movement.id} className="hover:shadow-sm transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Icon className={`w-5 h-5 ${typeInfo.color}`} />
                          <div>
                            <div className="font-medium">{typeInfo.label}</div>
                            <div className="text-sm text-gray-500">
                              Quantity: {movement.quantity}
                              {movement.reason && ` • ${movement.reason}`}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500">
                            {new Date(movement.createdAt).toLocaleDateString()}
                          </div>
                          {movement.totalCost && (
                            <div className="text-sm font-medium">
                              ${parseFloat(movement.totalCost).toFixed(2)}
                            </div>
                          )}
                        </div>
                      </div>
                      {movement.notes && (
                        <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                          {movement.notes}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No stock movements recorded</p>
              <p className="text-sm">Record your first stock movement to start tracking inventory changes</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
